'use server'

import { action } from '@/lib/actions/action'
import { disableCustomersOnInvoicy } from '@/lib/invoicy/disableCustomersOnInvoicy'
import { saveCustomersOnInvoicy } from '@/lib/invoicy/saveCustomersOnInvoicy'
import { saveCustomersOnRocketChat } from '@/lib/rocketchat/saveCustomersOnRocketChat'
import { saveCustomersOnSeafile } from '@/lib/seafile/saveCustomersOnSeafile'
import { db } from '@/src/db/db'
import { CustomersInputSchema } from '@/src/db/tables/customers.schemas'

export const saveCustomer = action

	.input(CustomersInputSchema)

	.handler(async ({ input }) => {
		const { nfseSettings, secrets, taxesConfigs, taxPasswords, taxTimelines, partners, payrollConfigs, taxNumbers, ...customerData } = input

		const customerId = customerData.id

		// Prepare secrets for vault.secrets table (remove data field and set id to customerId)
		const secretsWithoutData = secrets.map(({ data, ...secret }) => ({
			...secret,
			id: customerId,
		}))

		// Upload secrets to Supabase vault (needs to be done before creating or updating the customer)
		const { put } = await import('@/helpers/secrets/put')

		for (const secret of secrets) {
			// Extract type from description field (handle both JSON metadata and plain text)
			const type = (() => {
				try {
					const metadata = JSON.parse(secret.description || '{}')
					return metadata.type || secret.description || ''
				} catch {
					return secret.description || ''
				}
			})()

			const data = secret.data
			if (data && type) {
				const [savedSecret, savedSecretError] = await put({ customerId, type, data })
				if (!savedSecret) throw savedSecretError
			}
		}

		// create main record
		await db.customers.create(customerData).onConflict('id').merge()

		// create related records
		const result = await db.customers
			.find(customerId)
			.update({
				taxNumbers: { delete: { customerId } },
				nfseSettings: { delete: { customerId } },
				partners: { delete: { customerId } },
				payrollConfigs: { delete: { customerId } },
				taxesConfigs: { delete: { customerId } },
				taxTimelines: { delete: { customerId } },
				taxPasswords: { delete: { customerId } },
				secrets: { delete: { id: customerId } },
			})
			.update({
				nfseSettings: { create: nfseSettings },
				partners: { create: partners },
				payrollConfigs: { create: payrollConfigs },
				taxesConfigs: { create: taxesConfigs },
				taxNumbers: { create: taxNumbers },
				taxTimelines: { create: taxTimelines },
				taxPasswords: { create: taxPasswords },
				secrets: { create: secretsWithoutData },
			})

		// Create groups and departments on RocketChat (no need to do for each customer, only once)
		const [
			[rocketchatData, rocketchatError], //
			[invoicyData, invoicyError],
			[seafileData, seafileError],
			[disableInvoicyData, disableInvoicyError],
		] = await Promise.all([saveCustomersOnRocketChat(), saveCustomersOnInvoicy(), saveCustomersOnSeafile(), disableCustomersOnInvoicy()])

		if (!rocketchatData) throw rocketchatError
		if (!invoicyData) throw invoicyError
		if (!seafileData) throw seafileError
		if (!disableInvoicyData) throw disableInvoicyError

		return result
	})
