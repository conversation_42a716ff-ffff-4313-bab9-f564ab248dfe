import 'server-only'

import { action } from '@/lib/actions/action'
import { getSupabaseAdmin } from '@/lib/supabase/admin-client'
import { z } from 'zod'

const GetSecretInputSchema = z.object({
	customerId: z.string().uuid(),
	type: z.string(),
})

export const get = action
	.input(GetSecretInputSchema)
	.handler(async ({ input: { customerId, type } }) => {
		const supabase = getSupabaseAdmin()

		const { data: secret, error } = await supabase
			.from('vault.decrypted_secrets')
			.select('secret')
			.eq('id', customerId)
			.eq('description', type)
			.maybeSingle()

		if (error) throw new Error(`Failed to get secret: ${error.message}`)
		if (!secret) throw new Error(`Secret not found for customer ${customerId} and type ${type}`)

		return {
			value: secret.secret?.replaceAll('data:application/x-pkcs12;base64,', ''), // remove the data:application/x-pkcs12;base64, prefix. It was mistakingly added, but it lead to several bugs.,
		}
	})
